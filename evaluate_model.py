#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PPASR模型评估脚本
功能：
1. 评估模型在测试集上的性能
2. 生成详细的评估报告
3. 对比不同模型的性能
"""

import os
import sys
import json
import time
import subprocess
from pathlib import Path

def evaluate_model(model_path, config_path="configs/conformer_optimized.yml", test_manifest=None):
    """评估指定模型"""
    print(f"🔍 评估模型: {model_path}")
    
    # 检查模型文件
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return None
    
    # 检查配置文件
    if not os.path.exists(config_path):
        print(f"❌ 配置文件不存在: {config_path}")
        return None
    
    # 使用默认测试集
    if test_manifest is None:
        test_manifest = "dataset/manifest.test.fixed"
    
    if not os.path.exists(test_manifest):
        print(f"❌ 测试集文件不存在: {test_manifest}")
        return None
    
    print(f"📊 使用测试集: {test_manifest}")
    
    # 构建评估命令
    cmd = [
        "python", "eval.py",
        f"--configs={config_path}",
        f"--resume_model={model_path}"
    ]
    
    print(f"📝 执行命令: {' '.join(cmd)}")
    
    try:
        # 执行评估
        start_time = time.time()
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=600  # 10分钟超时
        )
        end_time = time.time()
        
        if result.returncode == 0:
            print("✅ 评估完成")
            
            # 解析评估结果
            output_lines = result.stdout.split('\n')
            cer = None
            wer = None
            
            for line in output_lines:
                if "CER:" in line:
                    try:
                        cer = float(line.split("CER:")[-1].strip().replace('%', ''))
                    except:
                        pass
                elif "WER:" in line:
                    try:
                        wer = float(line.split("WER:")[-1].strip().replace('%', ''))
                    except:
                        pass
            
            evaluation_result = {
                'model_path': model_path,
                'config_path': config_path,
                'test_manifest': test_manifest,
                'cer': cer,
                'wer': wer,
                'evaluation_time': end_time - start_time,
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                'output': result.stdout
            }
            
            print(f"🎯 CER: {cer}%" if cer else "❌ 无法解析CER")
            print(f"🎯 WER: {wer}%" if wer else "❌ 无法解析WER")
            print(f"⏱️  评估耗时: {end_time - start_time:.2f}秒")
            
            return evaluation_result
            
        else:
            print(f"❌ 评估失败，返回码: {result.returncode}")
            print(f"错误输出: {result.stderr}")
            return None
            
    except subprocess.TimeoutExpired:
        print("❌ 评估超时")
        return None
    except Exception as e:
        print(f"❌ 评估过程出错: {e}")
        return None

def find_model_files():
    """查找所有可用的模型文件"""
    models = []
    models_dir = Path("models")
    
    if not models_dir.exists():
        return models
    
    # 遍历模型目录
    for model_dir in models_dir.iterdir():
        if model_dir.is_dir():
            # 查找best_model和last_model
            for model_type in ['best_model', 'last_model']:
                model_path = model_dir / model_type
                if model_path.exists():
                    models.append({
                        'path': str(model_path),
                        'dir': model_dir.name,
                        'type': model_type,
                        'mtime': model_path.stat().st_mtime
                    })
    
    # 按修改时间排序
    models.sort(key=lambda x: x['mtime'], reverse=True)
    return models

def compare_models():
    """对比多个模型的性能"""
    print("🔍 查找可用模型...")
    models = find_model_files()
    
    if not models:
        print("❌ 没有找到可用的模型文件")
        return
    
    print(f"📊 找到 {len(models)} 个模型文件:")
    for i, model in enumerate(models[:10]):  # 只显示前10个
        print(f"  {i+1}. {model['dir']}/{model['type']} ({time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(model['mtime']))})")
    
    # 选择要评估的模型
    print("\n请选择要评估的模型 (输入数字，多个数字用逗号分隔，或输入'all'评估所有模型):")
    choice = input("选择: ").strip()
    
    selected_models = []
    if choice.lower() == 'all':
        selected_models = models[:5]  # 最多评估5个模型
    else:
        try:
            indices = [int(x.strip()) - 1 for x in choice.split(',')]
            selected_models = [models[i] for i in indices if 0 <= i < len(models)]
        except:
            print("❌ 无效的选择")
            return
    
    if not selected_models:
        print("❌ 没有选择有效的模型")
        return
    
    # 评估选中的模型
    results = []
    for model in selected_models:
        print(f"\n{'='*50}")
        result = evaluate_model(model['path'])
        if result:
            result['model_info'] = model
            results.append(result)
    
    # 生成对比报告
    if results:
        print(f"\n{'='*50}")
        print("📊 模型性能对比报告")
        print(f"{'='*50}")
        
        # 按CER排序
        results.sort(key=lambda x: x['cer'] if x['cer'] is not None else float('inf'))
        
        print(f"{'排名':<4} {'模型':<30} {'CER':<8} {'WER':<8} {'评估时间':<10}")
        print("-" * 70)
        
        for i, result in enumerate(results, 1):
            model_name = f"{result['model_info']['dir']}/{result['model_info']['type']}"
            cer = f"{result['cer']:.2f}%" if result['cer'] is not None else "N/A"
            wer = f"{result['wer']:.2f}%" if result['wer'] is not None else "N/A"
            eval_time = f"{result['evaluation_time']:.1f}s"
            
            print(f"{i:<4} {model_name:<30} {cer:<8} {wer:<8} {eval_time:<10}")
        
        # 保存详细报告
        report_file = f"evaluation_report_{int(time.time())}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细报告已保存到: {report_file}")
        
        # 显示最佳模型
        if results[0]['cer'] is not None:
            best_model = results[0]
            print(f"\n🏆 最佳模型: {best_model['model_info']['dir']}/{best_model['model_info']['type']}")
            print(f"   CER: {best_model['cer']:.2f}%")
            if best_model['wer'] is not None:
                print(f"   WER: {best_model['wer']:.2f}%")

def quick_test():
    """快速测试最新模型"""
    print("🚀 快速测试最新模型...")
    
    models = find_model_files()
    if not models:
        print("❌ 没有找到可用的模型文件")
        return
    
    # 使用最新的best_model
    best_models = [m for m in models if m['type'] == 'best_model']
    if best_models:
        latest_model = best_models[0]
    else:
        latest_model = models[0]
    
    print(f"📊 测试模型: {latest_model['dir']}/{latest_model['type']}")
    
    result = evaluate_model(latest_model['path'])
    if result:
        print("\n✅ 快速测试完成")
        if result['cer'] is not None:
            print(f"🎯 CER: {result['cer']:.2f}%")
        if result['wer'] is not None:
            print(f"🎯 WER: {result['wer']:.2f}%")

def main():
    """主函数"""
    print("🎯 PPASR模型评估工具")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        action = sys.argv[1]
    else:
        print("请选择操作:")
        print("1. 快速测试最新模型 (quick)")
        print("2. 对比多个模型 (compare)")
        print("3. 评估指定模型 (eval <model_path>)")
        action = input("输入选择: ").strip()
    
    if action in ['1', 'quick']:
        quick_test()
    elif action in ['2', 'compare']:
        compare_models()
    elif action in ['3', 'eval'] or action.startswith('eval '):
        if len(sys.argv) > 2:
            model_path = sys.argv[2]
        else:
            model_path = input("请输入模型路径: ").strip()
        
        if model_path:
            evaluate_model(model_path)
        else:
            print("❌ 未指定模型路径")
    else:
        print("❌ 无效的选择")
        sys.exit(1)

if __name__ == "__main__":
    main()
