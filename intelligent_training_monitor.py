#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能PPASR训练监控系统
功能：
1. 实时监控训练进度
2. 智能调整学习率
3. 自动早停机制
4. 动态配置优化
5. 异常检测和恢复
"""

import os
import sys
import time
import json
import yaml
import subprocess
import threading
import signal
import re
from pathlib import Path
from collections import deque
import numpy as np

class IntelligentTrainingMonitor:
    def __init__(self, config_path="configs/conformer_optimized.yml"):
        self.config_path = config_path
        self.training_process = None
        self.is_monitoring = False
        
        # 训练数据存储
        self.training_metrics = {
            'epochs': [],
            'steps': [],
            'train_loss': deque(maxlen=100),  # 保留最近100个值
            'test_cer': deque(maxlen=20),     # 保留最近20个CER值
            'learning_rates': deque(maxlen=100),
            'timestamps': []
        }
        
        # 性能指标
        self.best_cer = float('inf')
        self.best_model_epoch = 0
        self.no_improve_count = 0
        self.patience = 15  # 早停耐心值
        
        # 学习率调整参数
        self.lr_patience = 5  # 学习率调整耐心值
        self.lr_factor = 0.5  # 学习率衰减因子
        self.min_lr = 1e-6    # 最小学习率
        
        # 异常检测参数
        self.loss_explosion_threshold = 10.0  # loss爆炸阈值
        self.loss_stagnation_threshold = 0.001  # loss停滞阈值
        
        self.current_config = self.load_config()
        
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"❌ 加载配置文件失败: {e}")
            return None
    
    def save_config(self, config):
        """保存配置文件"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
            return True
        except Exception as e:
            print(f"❌ 保存配置文件失败: {e}")
            return False
    
    def start_training(self, resume_model=None):
        """启动训练进程"""
        print("🚀 启动智能训练监控...")
        print(f"📋 配置文件: {self.config_path}")
        
        if resume_model is None:
            resume_model = "models/conformer_streaming_fbank/best_model"
        
        # 构建训练命令
        cmd = ["python", "train.py", f"--configs={self.config_path}"]
        if os.path.exists(resume_model):
            cmd.append(f"--resume_model={resume_model}")
            print(f"🔄 恢复模型: {resume_model}")
        
        try:
            self.training_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            print("✅ 训练进程已启动")
            return True
        except Exception as e:
            print(f"❌ 启动训练失败: {e}")
            return False
    
    def parse_training_output(self, line):
        """解析训练输出并提取关键指标"""
        print(line)  # 实时显示
        
        current_time = time.time()
        
        # 解析训练步骤信息
        # 示例: Train epoch: [1/100], batch: [50/1446], loss: 59.56728, learning_rate: 0.00000008
        epoch_pattern = r'Train epoch:\s*\[(\d+)/\d+\].*?batch:\s*\[(\d+)/\d+\].*?loss:\s*([\d.]+).*?learning_rate:\s*([\d.e-]+)'
        match = re.search(epoch_pattern, line)
        if match:
            epoch, step, loss, lr = match.groups()
            epoch, step = int(epoch), int(step)
            loss, lr = float(loss), float(lr)
            
            self.training_metrics['epochs'].append(epoch)
            self.training_metrics['steps'].append(step)
            self.training_metrics['train_loss'].append(loss)
            self.training_metrics['learning_rates'].append(lr)
            self.training_metrics['timestamps'].append(current_time)
            
            # 检查异常情况
            self.check_training_anomalies(loss, lr)
        
        # 解析CER信息
        cer_pattern = r'CER:\s*([\d.]+)'
        match = re.search(cer_pattern, line)
        if match:
            cer = float(match.group(1))
            self.training_metrics['test_cer'].append(cer)
            
            # 更新最佳CER
            if cer < self.best_cer:
                self.best_cer = cer
                self.best_model_epoch = len(self.training_metrics['test_cer'])
                self.no_improve_count = 0
                print(f"🎉 新的最佳CER: {cer:.4f} (Epoch {self.best_model_epoch})")
            else:
                self.no_improve_count += 1
                print(f"📊 当前CER: {cer:.4f}, 最佳: {self.best_cer:.4f}, 无改善: {self.no_improve_count}/{self.patience}")
            
            # 智能调整策略
            self.intelligent_adjustment()
    
    def check_training_anomalies(self, loss, lr):
        """检查训练异常"""
        if len(self.training_metrics['train_loss']) < 5:
            return
        
        recent_losses = list(self.training_metrics['train_loss'])[-5:]
        
        # 检查loss爆炸
        if loss > self.loss_explosion_threshold:
            print(f"⚠️  检测到loss爆炸: {loss:.4f}")
            self.handle_loss_explosion()
        
        # 检查loss停滞
        if len(recent_losses) >= 5:
            loss_std = np.std(recent_losses)
            if loss_std < self.loss_stagnation_threshold:
                print(f"⚠️  检测到loss停滞，标准差: {loss_std:.6f}")
                self.handle_loss_stagnation()
    
    def handle_loss_explosion(self):
        """处理loss爆炸"""
        print("🔧 处理loss爆炸...")
        
        # 降低学习率
        new_lr = self.current_config['optimizer_conf']['learning_rate'] * 0.1
        if new_lr >= self.min_lr:
            self.adjust_learning_rate(new_lr)
            print(f"📉 紧急降低学习率到: {new_lr}")
        
        # 重启训练（从最佳模型）
        self.restart_training_from_best()
    
    def handle_loss_stagnation(self):
        """处理loss停滞"""
        print("🔧 处理loss停滞...")
        
        # 轻微降低学习率
        current_lr = self.current_config['optimizer_conf']['learning_rate']
        new_lr = current_lr * 0.8
        if new_lr >= self.min_lr:
            self.adjust_learning_rate(new_lr)
            print(f"📉 调整学习率: {current_lr} -> {new_lr}")
    
    def intelligent_adjustment(self):
        """智能调整策略"""
        if len(self.training_metrics['test_cer']) < 3:
            return
        
        # 学习率调整策略
        if self.no_improve_count >= self.lr_patience:
            current_lr = self.current_config['optimizer_conf']['learning_rate']
            new_lr = current_lr * self.lr_factor
            
            if new_lr >= self.min_lr:
                self.adjust_learning_rate(new_lr)
                print(f"🔄 自动调整学习率: {current_lr:.6f} -> {new_lr:.6f}")
                self.no_improve_count = 0  # 重置计数器
        
        # 早停检查
        if self.no_improve_count >= self.patience:
            print(f"⏹️  触发早停机制 (连续{self.patience}次无改善)")
            self.stop_training()
            return
        
        # 动态调整batch size（如果内存允许）
        if len(self.training_metrics['test_cer']) >= 10:
            recent_cer_trend = self.analyze_cer_trend()
            if recent_cer_trend == 'stagnant':
                self.suggest_batch_size_adjustment()
    
    def analyze_cer_trend(self):
        """分析CER趋势"""
        if len(self.training_metrics['test_cer']) < 5:
            return 'insufficient_data'
        
        recent_cers = list(self.training_metrics['test_cer'])[-5:]
        
        # 计算趋势
        improvements = sum(1 for i in range(1, len(recent_cers)) 
                          if recent_cers[i] < recent_cers[i-1])
        
        if improvements >= 3:
            return 'improving'
        elif improvements <= 1:
            return 'stagnant'
        else:
            return 'fluctuating'
    
    def adjust_learning_rate(self, new_lr):
        """调整学习率"""
        self.current_config['optimizer_conf']['learning_rate'] = new_lr
        
        # 保存新配置
        backup_path = f"{self.config_path}.backup"
        os.rename(self.config_path, backup_path)
        
        if self.save_config(self.current_config):
            print(f"✅ 学习率已调整并保存到配置文件")
        else:
            # 恢复备份
            os.rename(backup_path, self.config_path)
            print(f"❌ 保存配置失败，已恢复原配置")
    
    def suggest_batch_size_adjustment(self):
        """建议batch size调整"""
        current_batch_size = self.current_config['dataset_conf']['batch_size']
        
        if current_batch_size < 16:
            suggested_batch_size = min(current_batch_size * 2, 16)
            print(f"💡 建议增加batch size: {current_batch_size} -> {suggested_batch_size}")
            print("   这可能有助于稳定训练过程")
    
    def restart_training_from_best(self):
        """从最佳模型重启训练"""
        print("🔄 从最佳模型重启训练...")
        
        if self.training_process:
            self.training_process.terminate()
            time.sleep(2)
        
        # 寻找最佳模型
        best_model_path = self.find_best_model()
        if best_model_path:
            self.start_training(best_model_path)
    
    def find_best_model(self):
        """查找最佳模型"""
        models_dir = Path("models")
        best_model_candidates = []
        
        for model_dir in models_dir.iterdir():
            if model_dir.is_dir():
                best_model = model_dir / "best_model"
                if best_model.exists():
                    best_model_candidates.append(str(best_model))
        
        if best_model_candidates:
            # 返回最新的best_model
            return max(best_model_candidates, key=lambda x: Path(x).stat().st_mtime)
        
        return None
    
    def monitor_training(self):
        """监控训练过程"""
        if not self.training_process:
            print("❌ 没有运行的训练进程")
            return
        
        print("📊 开始智能监控...")
        self.is_monitoring = True
        
        try:
            while self.training_process.poll() is None and self.is_monitoring:
                line = self.training_process.stdout.readline()
                if line:
                    self.parse_training_output(line.strip())
                time.sleep(0.1)
        
        except KeyboardInterrupt:
            print("\n⏹️  用户中断监控")
            self.stop_training()
        except Exception as e:
            print(f"❌ 监控过程出错: {e}")
    
    def stop_training(self):
        """停止训练"""
        if self.training_process:
            print("⏹️  正在停止训练...")
            self.training_process.terminate()
            time.sleep(2)
            if self.training_process.poll() is None:
                self.training_process.kill()
            print("✅ 训练已停止")
        self.is_monitoring = False
    
    def get_training_summary(self):
        """获取训练摘要"""
        if not self.training_metrics['epochs']:
            return "没有训练数据"
        
        total_epochs = len(set(self.training_metrics['epochs']))
        avg_loss = np.mean(list(self.training_metrics['train_loss'])[-10:]) if self.training_metrics['train_loss'] else 0
        
        summary = f"""
📊 智能训练监控摘要
====================
总训练轮数: {total_epochs}
最佳CER: {self.best_cer:.4f} (Epoch {self.best_model_epoch})
当前无改善次数: {self.no_improve_count}/{self.patience}

最近平均训练损失: {avg_loss:.4f}
最近CER: {list(self.training_metrics['test_cer'])[-1]:.4f if self.training_metrics['test_cer'] else 'N/A'}
当前学习率: {self.current_config['optimizer_conf']['learning_rate']:.6f}

训练状态: {'运行中' if self.is_monitoring else '已停止'}
"""
        return summary
    
    def generate_recommendations(self):
        """生成训练建议"""
        recommendations = []
        
        if self.best_cer > 0.3:
            recommendations.append("🔍 CER仍然较高，建议检查数据质量和标注准确性")
        
        if len(self.training_metrics['test_cer']) >= 5:
            trend = self.analyze_cer_trend()
            if trend == 'stagnant':
                recommendations.append("📈 CER停滞，建议调整模型结构或数据增强策略")
            elif trend == 'improving':
                recommendations.append("✅ CER持续改善，当前策略有效")
        
        if self.current_config['optimizer_conf']['learning_rate'] <= self.min_lr:
            recommendations.append("⚠️  学习率已达到最小值，考虑重新初始化或调整其他超参数")
        
        if not recommendations:
            recommendations.append("🎯 训练进展良好，继续当前设置")
        
        return recommendations

def main():
    """主函数"""
    print("🎯 智能PPASR训练监控系统")
    print("=" * 50)
    
    # 创建智能监控器
    monitor = IntelligentTrainingMonitor()
    
    # 设置信号处理
    def signal_handler(sig, frame):
        print("\n🛑 接收到中断信号，正在停止...")
        monitor.stop_training()
        
        # 显示最终摘要
        print("\n" + "=" * 50)
        print(monitor.get_training_summary())
        
        print("\n💡 最终建议:")
        for rec in monitor.generate_recommendations():
            print(f"  {rec}")
        
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    try:
        # 启动智能训练
        if monitor.start_training():
            # 启动监控线程
            monitor_thread = threading.Thread(target=monitor.monitor_training)
            monitor_thread.daemon = True
            monitor_thread.start()
            
            # 等待训练完成
            monitor_thread.join()
            
            # 显示最终结果
            print("\n" + "=" * 50)
            print(monitor.get_training_summary())
            
            print("\n💡 训练建议:")
            for rec in monitor.generate_recommendations():
                print(f"  {rec}")
        
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
    
    print("\n🏁 智能训练监控结束")

if __name__ == "__main__":
    main()
