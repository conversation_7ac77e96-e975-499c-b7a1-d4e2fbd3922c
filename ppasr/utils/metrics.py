try:
    import Levenshtein as Lev
    # 尝试不同的导入方式
    if not hasattr(Lev, 'distance'):
        from Levenshtein import distance as lev_distance
    else:
        lev_distance = Lev.distance
except ImportError:
    # 如果Levenshtein不可用，使用简单的实现
    def lev_distance(s1, s2):
        if len(s1) < len(s2):
            return lev_distance(s2, s1)
        if len(s2) == 0:
            return len(s1)
        previous_row = list(range(len(s2) + 1))
        for i, c1 in enumerate(s1):
            current_row = [i + 1]
            for j, c2 in enumerate(s2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row
        return previous_row[-1]


def cer(prediction, label):
    """
   通过计算两个字符串的距离，得出字错率

    Arguments:
        prediction (string): 比较的字符串
        label (string): 比较的字符串
    """
    prediction, label, = prediction.replace(" ", ""), label.replace(" ", "")
    if len(label) == 0:
        return 0.0 if len(prediction) == 0 else 1.0
    return lev_distance(prediction, label) / float(len(label))


def wer(prediction, label):
    start = 34
    word_dict = {}
    prediction = prediction.split(" ")
    label = label.split(" ")
    for s in prediction:
        if s not in word_dict.keys():
            word_dict[s] = start + len(word_dict)
    for s in label:
        if s not in word_dict.keys():
            word_dict[s] = start + len(word_dict)
    s3 = ''.join([chr(word_dict[k]) for k in prediction])
    s4 = ''.join([chr(word_dict[k]) for k in label])
    return cer(s3, s4)
