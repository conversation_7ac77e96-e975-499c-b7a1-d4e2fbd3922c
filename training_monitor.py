#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PPASR训练监控和管理系统
功能：
1. 启动训练并监控进度
2. 实时显示训练指标
3. 自动早停机制
4. 学习率调整建议
5. 模型评估
"""

import os
import sys
import time
import json
import subprocess
import threading
import signal
from pathlib import Path
import matplotlib.pyplot as plt
import numpy as np

class TrainingMonitor:
    def __init__(self, config_path="configs/conformer_optimized.yml", 
                 resume_model="models/conformer_streaming_fbank/best_model"):
        self.config_path = config_path
        self.resume_model = resume_model
        self.training_process = None
        self.is_monitoring = False
        self.training_data = {
            'epochs': [],
            'train_loss': [],
            'test_loss': [],
            'cer': [],
            'learning_rates': []
        }
        self.best_cer = float('inf')
        self.patience = 10  # 早停耐心值
        self.no_improve_count = 0
        
    def start_training(self):
        """启动训练进程"""
        print("🚀 启动PPASR训练...")
        print(f"配置文件: {self.config_path}")
        print(f"恢复模型: {self.resume_model}")
        
        # 构建训练命令
        cmd = [
            "python", "train.py",
            f"--configs={self.config_path}",
            f"--resume_model={self.resume_model}"
        ]
        
        try:
            # 启动训练进程
            self.training_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            print("✅ 训练进程已启动")
            return True
            
        except Exception as e:
            print(f"❌ 启动训练失败: {e}")
            return False
    
    def monitor_training(self):
        """监控训练进程"""
        if not self.training_process:
            print("❌ 没有运行的训练进程")
            return
            
        print("📊 开始监控训练进度...")
        self.is_monitoring = True
        
        try:
            while self.training_process.poll() is None and self.is_monitoring:
                line = self.training_process.stdout.readline()
                if line:
                    self.parse_training_output(line.strip())
                time.sleep(0.1)
                
        except KeyboardInterrupt:
            print("\n⏹️  用户中断监控")
            self.stop_training()
        except Exception as e:
            print(f"❌ 监控过程出错: {e}")
    
    def parse_training_output(self, line):
        """解析训练输出"""
        print(line)  # 实时显示训练日志
        
        # 解析训练指标
        if "Epoch:" in line and "loss:" in line:
            try:
                # 示例: [2025-08-21 12:30:15] Epoch: 1/100, Step: 50/1447, loss: 2.345, lr: 0.0001
                parts = line.split(',')
                epoch_info = [p for p in parts if "Epoch:" in p][0]
                loss_info = [p for p in parts if "loss:" in p][0]
                lr_info = [p for p in parts if "lr:" in p][0] if any("lr:" in p for p in parts) else None
                
                epoch = int(epoch_info.split('/')[0].split(':')[-1].strip())
                loss = float(loss_info.split(':')[-1].strip())
                lr = float(lr_info.split(':')[-1].strip()) if lr_info else None
                
                self.training_data['epochs'].append(epoch)
                self.training_data['train_loss'].append(loss)
                if lr:
                    self.training_data['learning_rates'].append(lr)
                    
            except Exception as e:
                pass  # 解析失败时忽略
        
        # 解析测试结果
        if "Test CER:" in line:
            try:
                cer = float(line.split("Test CER:")[-1].strip())
                self.training_data['cer'].append(cer)
                
                # 检查是否有改善
                if cer < self.best_cer:
                    self.best_cer = cer
                    self.no_improve_count = 0
                    print(f"🎉 新的最佳CER: {cer:.4f}")
                else:
                    self.no_improve_count += 1
                    print(f"📈 当前CER: {cer:.4f}, 最佳CER: {self.best_cer:.4f}, 无改善次数: {self.no_improve_count}")
                
                # 早停检查
                if self.no_improve_count >= self.patience:
                    print(f"⏹️  触发早停机制 (连续{self.patience}次无改善)")
                    self.stop_training()
                    
            except Exception as e:
                pass
    
    def stop_training(self):
        """停止训练"""
        if self.training_process:
            print("⏹️  正在停止训练...")
            self.training_process.terminate()
            time.sleep(2)
            if self.training_process.poll() is None:
                self.training_process.kill()
            print("✅ 训练已停止")
        self.is_monitoring = False
    
    def plot_training_curves(self):
        """绘制训练曲线"""
        if not self.training_data['epochs']:
            print("❌ 没有训练数据可绘制")
            return
            
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('PPASR Training Progress', fontsize=16)
        
        # 训练损失
        if self.training_data['train_loss']:
            axes[0, 0].plot(self.training_data['epochs'][:len(self.training_data['train_loss'])], 
                           self.training_data['train_loss'])
            axes[0, 0].set_title('Training Loss')
            axes[0, 0].set_xlabel('Epoch')
            axes[0, 0].set_ylabel('Loss')
            axes[0, 0].grid(True)
        
        # CER
        if self.training_data['cer']:
            axes[0, 1].plot(range(len(self.training_data['cer'])), self.training_data['cer'])
            axes[0, 1].set_title('Character Error Rate (CER)')
            axes[0, 1].set_xlabel('Evaluation Step')
            axes[0, 1].set_ylabel('CER')
            axes[0, 1].grid(True)
        
        # 学习率
        if self.training_data['learning_rates']:
            axes[1, 0].plot(self.training_data['epochs'][:len(self.training_data['learning_rates'])], 
                           self.training_data['learning_rates'])
            axes[1, 0].set_title('Learning Rate')
            axes[1, 0].set_xlabel('Epoch')
            axes[1, 0].set_ylabel('Learning Rate')
            axes[1, 0].grid(True)
        
        # 改善趋势
        if self.training_data['cer']:
            best_cers = []
            current_best = float('inf')
            for cer in self.training_data['cer']:
                if cer < current_best:
                    current_best = cer
                best_cers.append(current_best)
            
            axes[1, 1].plot(range(len(self.training_data['cer'])), self.training_data['cer'], 
                           label='Current CER', alpha=0.7)
            axes[1, 1].plot(range(len(best_cers)), best_cers, 
                           label='Best CER', linewidth=2)
            axes[1, 1].set_title('CER Improvement Trend')
            axes[1, 1].set_xlabel('Evaluation Step')
            axes[1, 1].set_ylabel('CER')
            axes[1, 1].legend()
            axes[1, 1].grid(True)
        
        plt.tight_layout()
        plt.savefig('training_progress.png', dpi=300, bbox_inches='tight')
        print("📊 训练曲线已保存到 training_progress.png")
        plt.show()
    
    def get_training_summary(self):
        """获取训练摘要"""
        if not self.training_data['epochs']:
            return "没有训练数据"
            
        summary = f"""
📊 训练摘要
====================
总训练轮数: {len(self.training_data['epochs'])}
最佳CER: {self.best_cer:.4f}
当前无改善次数: {self.no_improve_count}/{self.patience}

最近训练损失: {self.training_data['train_loss'][-1]:.4f if self.training_data['train_loss'] else 'N/A'}
最近CER: {self.training_data['cer'][-1]:.4f if self.training_data['cer'] else 'N/A'}
当前学习率: {self.training_data['learning_rates'][-1]:.6f if self.training_data['learning_rates'] else 'N/A'}
"""
        return summary
    
    def suggest_adjustments(self):
        """提供训练调整建议"""
        suggestions = []
        
        if len(self.training_data['cer']) >= 5:
            recent_cers = self.training_data['cer'][-5:]
            if all(cer >= recent_cers[0] for cer in recent_cers):
                suggestions.append("🔄 CER停止下降，建议降低学习率")
        
        if len(self.training_data['train_loss']) >= 10:
            recent_losses = self.training_data['train_loss'][-10:]
            if np.std(recent_losses) < 0.01:
                suggestions.append("📉 训练损失趋于稳定，可能需要调整模型结构")
        
        if self.best_cer > 0.5:
            suggestions.append("⚠️  CER仍然很高，检查数据质量和模型配置")
        
        if not suggestions:
            suggestions.append("✅ 训练进展良好，继续当前设置")
        
        return suggestions

def main():
    """主函数"""
    print("🎯 PPASR训练监控系统")
    print("=" * 50)
    
    # 创建监控器
    monitor = TrainingMonitor()
    
    # 设置信号处理
    def signal_handler(sig, frame):
        print("\n🛑 接收到中断信号，正在停止...")
        monitor.stop_training()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    try:
        # 启动训练
        if monitor.start_training():
            # 启动监控线程
            monitor_thread = threading.Thread(target=monitor.monitor_training)
            monitor_thread.daemon = True
            monitor_thread.start()
            
            # 等待训练完成或用户中断
            monitor_thread.join()
            
            # 显示结果
            print("\n" + "=" * 50)
            print(monitor.get_training_summary())
            
            # 显示建议
            print("\n💡 训练建议:")
            for suggestion in monitor.suggest_adjustments():
                print(f"  {suggestion}")
            
            # 绘制训练曲线
            try:
                monitor.plot_training_curves()
            except Exception as e:
                print(f"⚠️  绘制训练曲线失败: {e}")
        
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
    
    print("\n🏁 训练监控结束")

if __name__ == "__main__":
    main()
