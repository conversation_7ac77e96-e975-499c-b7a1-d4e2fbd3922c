#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的PPASR训练启动和监控脚本
"""

import os
import sys
import time
import subprocess
import threading
from pathlib import Path

def monitor_training_log():
    """监控训练日志"""
    log_files = [
        "logs/training.log",
        "models/conformer_streaming_fbank/train-log"
    ]
    
    print("📊 开始监控训练日志...")
    
    # 找到最新的模型目录
    models_dir = Path("models")
    if models_dir.exists():
        model_dirs = [d for d in models_dir.iterdir() if d.is_dir()]
        if model_dirs:
            # 按修改时间排序，获取最新的
            latest_model_dir = max(model_dirs, key=lambda x: x.stat().st_mtime)
            train_log = latest_model_dir / "train-log"
            if train_log.exists():
                log_files.append(str(train_log))
    
    # 监控日志文件
    for log_file in log_files:
        if os.path.exists(log_file):
            print(f"📄 监控日志文件: {log_file}")
            try:
                # 使用tail -f监控日志
                process = subprocess.Popen(
                    ["tail", "-f", log_file],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    universal_newlines=True
                )
                
                while True:
                    line = process.stdout.readline()
                    if line:
                        print(f"[LOG] {line.strip()}")
                        
                        # 检查关键指标
                        if "CER:" in line:
                            print(f"🎯 {line.strip()}")
                        elif "loss:" in line:
                            print(f"📉 {line.strip()}")
                        elif "best model" in line.lower():
                            print(f"🏆 {line.strip()}")
                    else:
                        time.sleep(1)
                        
            except KeyboardInterrupt:
                print("\n⏹️  停止监控日志")
                process.terminate()
                break
            except Exception as e:
                print(f"❌ 监控日志出错: {e}")
                continue

def start_training():
    """启动训练"""
    print("🚀 启动PPASR训练...")
    
    # 检查配置文件
    config_file = "configs/conformer_optimized.yml"
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    # 检查恢复模型
    resume_model = "models/conformer_streaming_fbank/best_model"
    if not os.path.exists(resume_model):
        print(f"❌ 恢复模型不存在: {resume_model}")
        return False
    
    # 构建训练命令
    cmd = [
        "python", "train.py",
        f"--configs={config_file}",
        f"--resume_model={resume_model}"
    ]
    
    print(f"📝 执行命令: {' '.join(cmd)}")
    
    try:
        # 启动训练进程（后台运行）
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        print("✅ 训练进程已启动")
        print(f"🆔 进程ID: {process.pid}")
        
        # 实时显示输出
        try:
            while True:
                line = process.stdout.readline()
                if not line:
                    break
                    
                print(line.strip())
                
                # 检查关键信息
                if "训练数据：" in line:
                    print(f"📊 {line.strip()}")
                elif "成功恢复模型参数" in line:
                    print(f"🔄 {line.strip()}")
                elif "CER:" in line:
                    print(f"🎯 {line.strip()}")
                elif "loss:" in line and "Epoch:" in line:
                    print(f"📉 {line.strip()}")
                    
        except KeyboardInterrupt:
            print("\n⏹️  用户中断训练")
            process.terminate()
            return False
            
        # 等待进程结束
        return_code = process.wait()
        if return_code == 0:
            print("✅ 训练完成")
            return True
        else:
            print(f"❌ 训练异常结束，返回码: {return_code}")
            return False
            
    except Exception as e:
        print(f"❌ 启动训练失败: {e}")
        return False

def check_training_status():
    """检查训练状态"""
    print("🔍 检查训练状态...")
    
    # 检查是否有训练进程在运行
    try:
        result = subprocess.run(
            ["ps", "aux"],
            capture_output=True,
            text=True
        )
        
        training_processes = []
        for line in result.stdout.split('\n'):
            if 'python' in line and 'train.py' in line:
                training_processes.append(line)
        
        if training_processes:
            print("🟢 发现运行中的训练进程:")
            for proc in training_processes:
                print(f"  {proc}")
        else:
            print("🔴 没有发现运行中的训练进程")
            
    except Exception as e:
        print(f"❌ 检查进程状态失败: {e}")
    
    # 检查最新的模型文件
    models_dir = Path("models")
    if models_dir.exists():
        model_dirs = [d for d in models_dir.iterdir() if d.is_dir()]
        if model_dirs:
            latest_model_dir = max(model_dirs, key=lambda x: x.stat().st_mtime)
            print(f"📁 最新模型目录: {latest_model_dir}")
            
            # 检查训练日志
            train_log = latest_model_dir / "train-log"
            if train_log.exists():
                print(f"📄 训练日志: {train_log}")
                # 显示最后几行
                try:
                    result = subprocess.run(
                        ["tail", "-10", str(train_log)],
                        capture_output=True,
                        text=True
                    )
                    print("📋 最近的训练日志:")
                    for line in result.stdout.split('\n'):
                        if line.strip():
                            print(f"  {line}")
                except Exception as e:
                    print(f"❌ 读取训练日志失败: {e}")

def main():
    """主函数"""
    print("🎯 PPASR训练管理工具")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        action = sys.argv[1]
    else:
        print("请选择操作:")
        print("1. 启动训练 (start)")
        print("2. 检查状态 (status)")
        print("3. 监控日志 (monitor)")
        action = input("输入选择 (1/2/3 或 start/status/monitor): ").strip()
    
    if action in ['1', 'start']:
        start_training()
    elif action in ['2', 'status']:
        check_training_status()
    elif action in ['3', 'monitor']:
        monitor_training_log()
    else:
        print("❌ 无效的选择")
        sys.exit(1)

if __name__ == "__main__":
    main()
