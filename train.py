import argparse
import functools
import logging
import os
import yaml
from ppasr.trainer import PPASRTrainer
from ppasr.utils.utils import add_arguments, print_arguments

# 定义日志保存路径
def setup_logging(log_dir, log_file):
    os.makedirs(log_dir, exist_ok=True)  # 创建日志目录（如果不存在）
    log_path = os.path.join(log_dir, log_file)
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_path),  # 保存到文件
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )
    return logging.getLogger()

# 解析命令行参数
parser = argparse.ArgumentParser(description=__doc__)
add_arg = functools.partial(add_arguments, argparser=parser)
add_arg('configs',          str,    'configs/conformer.yml',       '配置文件')
add_arg("use_gpu",          bool,   True,                          '是否使用GPU训练')
add_arg('augment_conf_path',str,    'configs/augmentation.json',   '数据增强的配置文件，为json格式')
add_arg('save_model_path',  str,    'models/',                     '模型保存的路径')
add_arg('resume_model',     str,    None,                          '恢复训练，当为None则不使用预训练模型')
add_arg('pretrained_model', str,    None,                          '预训练模型的路径，当为None则不使用预训练模型')
add_arg('log_dir',          str,    'logs',                        '日志保存的目录')  # 添加日志目录参数
add_arg('log_file',         str,    'training.log',                '日志文件名称')  # 添加日志文件名称参数

args = parser.parse_args()
print_arguments(args=args)

# 设置日志
logger = setup_logging(log_dir=args.log_dir, log_file=args.log_file)

# 打印日志
logger.info("开始训练...")
logger.info(f"配置文件路径: {args.configs}")
logger.info(f"模型保存路径: {args.save_model_path}")
logger.info(f"日志保存路径: {os.path.join(args.log_dir, args.log_file)}")

# 获取训练器
trainer = PPASRTrainer(configs=args.configs, use_gpu=args.use_gpu)

# 开始训练
trainer.train(save_model_path=args.save_model_path,
              resume_model=args.resume_model,
              pretrained_model=args.pretrained_model,
              augment_conf_path=args.augment_conf_path)
