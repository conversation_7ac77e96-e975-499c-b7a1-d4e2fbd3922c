from pathlib import Path
import shutil
import re

def batch_copy_wav_files(source_dir, target_dir):
    """
    批量复制S0001-S0916子目录中的.wav文件到目标目录
    :param source_dir: 源目录路径（字符串或Path对象）
    :param target_dir: 目标目录路径（字符串或Path对象）
    """
    src = Path(source_dir)
    dst = Path(target_dir)
    
    # 创建目标目录（如果不存在）
    dst.mkdir(parents=True, exist_ok=True)
    
    # 定义子目录名称的正则匹配规则（S后接4位数字）
    dir_pattern = re.compile(r'^S(\d{4})$', re.IGNORECASE)
    
    # 遍历源目录所有直接子目录
    for subdir in src.iterdir():
        if subdir.is_dir():
            dir_match = dir_pattern.match(subdir.name)
            if dir_match:
                # 验证数字范围（0001-0916）
                dir_number = int(dir_match.group(1))
                if 1 <= dir_number <= 916:
                    print(f"正在处理目录: {subdir.name} ({dir_number:04d})")
                    
                    # 查找子目录中的所有.wav文件
                    for wav_file in subdir.glob('*.wav'):
                        try:
                            # 构建目标文件路径（保留原始文件名）
                            target_path = dst / wav_file.name
                            
                            # 复制文件并保留元数据
                            shutil.copy2(wav_file, target_path)
                            print(f"  成功复制: {wav_file.name}")
                            
                        except PermissionError:
                            print(f"  权限错误: {wav_file.name} 无法访问")
                        except FileNotFoundError:
                            print(f"  文件缺失: {wav_file.name}")
                        except Exception as e:
                            print(f"  复制失败: {wav_file.name} - {str(e)}")


# 使用示例
a_path = "data_aishell/wav/dev1"  # 替换为你的源目录路径
b_path = "data_aishell/wav/dev"  # 替换为目标目录路径
batch_copy_wav_files(a_path, b_path)




