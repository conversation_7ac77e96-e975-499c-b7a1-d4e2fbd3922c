import re

def add_wav_to_numbers(input_file, output_file):
    # 正则表达式匹配所有字母数字组合（连续出现的字母或数字）
    pattern = r'([A-Za-z0-9]+)'
    
    with open(input_file, 'r', encoding='utf-8') as infile, \
         open(output_file, 'w', encoding='utf-8') as outfile:
        for line in infile:
            # 在每行的匹配项后添加".wav"
            modified_line = re.sub(pattern, r'\1.wav', line)
            outfile.write(modified_line)

# 使用示例
input_path = 'aishell_transcript_v0.8.txt'   # 输入文件路径
output_path = '22222.txt' # 输出文件路径
add_wav_to_numbers(input_path, output_path)
