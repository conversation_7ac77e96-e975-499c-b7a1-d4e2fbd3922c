import os
import tarfile

# 定义源文件夹和目标文件夹
source_folder = 'wav/test'
target_folder = 'wav/b'

# 确保目标文件夹存在
if not os.path.exists(target_folder):
    os.makedirs(target_folder)

# 遍历源文件夹中的所有文件
for filename in os.listdir(source_folder):
    # 检查文件是否为tar.gz格式
    if filename.endswith('.tar.gz'):
        # 构造完整的文件路径
        file_path = os.path.join(source_folder, filename)
        # 打开并解压tar.gz文件
        with tarfile.open(file_path, 'r:gz') as tar:
            # 解压到目标文件夹
            tar.extractall(path=target_folder)
            print(f"解压完成：{filename}")

print("所有tar.gz文件已解压到'b'文件夹中。")
