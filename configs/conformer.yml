# 编码器参数
encoder_conf:
  output_size: 256    # Attention的维度，匹配预训练模型的维度
  attention_heads: 4  # Attention头的数量，匹配预训练模型
  linear_units: 2048  # the number of units of position-wise feed forward
  num_blocks: 12      # the number of encoder blocks
  dropout_rate: 0.1
  positional_dropout_rate: 0.1
  attention_dropout_rate: 0.1
  input_layer: 'conv2d' # encoder input type, you can chose conv2d, conv2d6 and conv2d8
  normalize_before: True
  cnn_module_kernel: 15  # 匹配预训练模型的卷积核大小
  use_cnn_module: True
  activation_type: 'swish'
  pos_enc_layer_type: 'rel_pos'

# 解码器参数
decoder_conf:
  attention_heads: 4  # Attention头的数量，匹配预训练模型
  linear_units: 1024  # the number of units of position-wise feed forward，匹配预训练模型
  num_blocks: 3
  r_num_blocks: 3
  dropout_rate: 0.1
  positional_dropout_rate: 0.1
  self_attention_dropout_rate: 0.1
  src_attention_dropout_rate: 0.1

# 模型其他参数配置
model_conf:
  ctc_weight: 0.4           # 0.3
  lsm_weight: 0.2               # 0.1
  reverse_weight: 0.4            # 0.3
  length_normalized_loss: false

# 数据集参数
dataset_conf:
  # 训练的批量大小
  batch_size: 16
  # 读取数据的线程数量
  num_workers: 4
  # 缓存的 mini-batch 的个数
  prefetch_factor: 2
  # 是否使用共享内存
  use_shared_memory: True
  # 过滤最短的音频长度
  min_duration: 0.5
  # 过滤最长的音频长度，当为-1的时候不限制长度
  max_duration: 20
  # 训练数据的数据列表路径
  train_manifest: 'dataset/manifest.train'
  # 测试数据的数据列表路径
  test_manifest: 'dataset/manifest.test'
  # 数据字典的路径
  dataset_vocab: 'dataset/vocabulary.txt'
  # 均值和标准值得json文件路径，后缀 (.json)
  mean_istd_path: 'dataset/mean_istd.json'
  # 噪声数据列表文件
  noise_manifest_path: 'dataset/manifest.noise'
  # 数据列表类型，支持txt、binary
  manifest_type: 'txt'

# 数据预处理参数
preprocess_conf:
  # 音频预处理方法，支持：linear、mfcc、fbank
  feature_method: 'fbank'
  # 计算fbank得到的mel大小
  n_mels: 80
  # 计算mfcc得到的mfcc大小
  n_mfcc: 40
  # 音频的采样率
  sample_rate: 16000
  # 是否对音频进行音量归一化
  use_dB_normalization: True
  # 对音频进行音量归一化的音量分贝值
  target_dB: -20

# ctc_beam_search解码器参数
ctc_beam_search_decoder_conf:
  # 集束搜索的LM系数
  alpha: 2.21
  # 集束搜索的WC系数
  beta: 1.95
  # 集束搜索的大小，范围:[5, 500]
  beam_size: 300
  # 集束搜索方法使用CPU数量
  num_processes: 10
  # 剪枝的概率
  cutoff_prob: 0.99
  # 剪枝的最大值
  cutoff_top_n: 40
  # 语言模型文件路径
  language_model_path: 'lm/my_lm.klm'

# 优化方法参数配置
optimizer_conf:
  # 优化方法，支持Adam、AdamW
  optimizer: 'AdamW'
  # 权重衰减系数
  weight_decay: 1.e-6
  # 初始学习率的大小
  learning_rate: 0.0005
  # 学习率衰减方法，支持WarmupLR、NoamHoldAnnealing
  scheduler: 'WarmupLR'
  # 学习率衰减方法参数配置
  scheduler_conf:
    # 学习率预热步数，对应的是step/accum_grad
    warmup_steps: 40000
    # 最小学习率
    min_lr: 1.e-5

# 训练参数配置
train_conf:
  # 是否开启自动混合精度
  enable_amp: True # False
  # 梯度裁剪
  grad_clip: 5.0
  # 梯度累加，变相扩大batch_size的作用
  accum_grad: 4
  # 训练的轮数
  max_epoch: 600
  # 多少batch打印一次日志
  log_interval: 100

# 所使用的模型
use_model: 'conformer'
# 是否流式模型
streaming: Flase
# 结果解码方法，支持：ctc_beam_search、ctc_greedy
decoder: 'ctc_beam_search'
# 计算错误率方法，支持：cer、wer
metrics_type: 'cer'
