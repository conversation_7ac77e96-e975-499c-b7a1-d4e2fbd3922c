ctc_beam_search_decoder_conf:
  alpha: 2.21
  beam_size: 300
  beta: 1.95
  cutoff_prob: 0.99
  cutoff_top_n: 40
  language_model_path: lm/my_lm.klm
  num_processes: 10
dataset_conf:
  batch_size: 8
  dataset_vocab: dataset/vocabulary.txt
  manifest_type: txt
  max_duration: 20
  mean_istd_path: dataset/mean_istd.json
  min_duration: 0.5
  noise_manifest_path: dataset/manifest.noise
  num_workers: 4
  prefetch_factor: 2
  test_manifest: dataset/manifest.test.fixed
  train_manifest: dataset/manifest.train.fixed
  use_shared_memory: true
decoder: ctc_beam_search
decoder_conf:
  attention_heads: 4
  dropout_rate: 0.1
  linear_units: 1024
  num_blocks: 3
  positional_dropout_rate: 0.1
  r_num_blocks: 3
  self_attention_dropout_rate: 0.1
  src_attention_dropout_rate: 0.1
encoder_conf:
  activation_type: swish
  attention_dropout_rate: 0.1
  attention_heads: 4
  cnn_module_kernel: 15
  dropout_rate: 0.1
  input_layer: conv2d
  linear_units: 2048
  normalize_before: true
  num_blocks: 12
  output_size: 256
  pos_enc_layer_type: rel_pos
  positional_dropout_rate: 0.1
  use_cnn_module: true
metrics_type: cer
model_conf:
  ctc_weight: 0.3
  length_normalized_loss: false
  lsm_weight: 0.1
  reverse_weight: 0.3
optimizer_conf:
  learning_rate: 0.0001
  optimizer: AdamW
  scheduler: WarmupLR
  scheduler_conf:
    min_lr: 1.0e-06
    warmup_steps: 10000
  weight_decay: 1.0e-06
preprocess_conf:
  feature_method: fbank
  n_mels: 80
  n_mfcc: 40
  sample_rate: 16000
  target_dB: -20
  use_dB_normalization: true
streaming: false
train_conf:
  accum_grad: 8
  enable_amp: true
  grad_clip: 5.0
  log_interval: 50
  max_epoch: 100
use_model: conformer
